#!/usr/bin/env node

/**
 * Test script to demonstrate the logging functionality
 */

const logger = require("./app/services/logger");
const chalk = require("chalk");

async function testLogging() {
  console.log(chalk.blue.bold("\n🧪 Testing Logging Service\n"));

  // Wait for logger initialization
  await new Promise((resolve) => setTimeout(resolve, 100));

  // Test basic logging levels
  await logger.info("Application started");
  await logger.debug(
    "Debug information - this might not show depending on log level"
  );
  await logger.warn("This is a warning message");
  await logger.error("This is an error message");
  await logger.success("Operation completed successfully");

  // Test crawler activity logging
  await logger.logCrawlerActivity("initialization", "started");
  await logger.logCrawlerActivity("site_crawl", "success", {
    siteName: "test-site",
    propertiesFound: 25,
    pageNumber: 1,
  });
  await logger.logCrawlerActivity("initialization", "completed");

  // Test system activity logging
  await logger.logSystemActivity("browser", "launch", "success");
  await logger.logSystemActivity("database", "save", "success", {
    recordsCount: 100,
    tableName: "properties",
  });
  await logger.logSystemActivity("proxy", "connect", "failed", {
    proxyUrl: "http://proxy.example.com:8080",
    error: "Connection timeout",
  });

  // Test error logging with stack trace
  try {
    throw new Error("Test error for logging");
  } catch (error) {
    await logger.logCrawlerActivity("test_operation", "failed", {
      error: error.message,
      stack: error.stack,
    });
  }

  console.log(chalk.green("\n✅ Logging test completed!"));
  console.log(
    chalk.gray("Check the logs directory for the generated log file.")
  );

  // Show log statistics
  console.log(chalk.blue("\n📊 Log Statistics:"));
  const stats = await logger.getLogStats();
  if (stats.error) {
    console.log(chalk.red("Error getting stats:", stats.error));
  } else {
    console.log(`File size: ${(stats.fileSize / 1024).toFixed(2)} KB`);
    console.log(`Total entries: ${stats.totalEntries}`);
    console.log(`Last modified: ${stats.lastModified}`);
    console.log("Log level counts:", stats.logCounts);
  }
}

// Run the test
if (require.main === module) {
  testLogging().catch(console.error);
}

module.exports = testLogging;
