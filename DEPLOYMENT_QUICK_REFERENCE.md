# 🚀 Deployment Quick Reference

## One-Command Deployment

### Step 1: Transfer Files

```bash
# From your local machine
rsync -avz --exclude 'node_modules' --exclude '.git' --exclude '.DS_Store' . victor@*************:/home/<USER>/crawler-notifier
```

### Step 2: Initial Setup

```bash
# SSH to server
ssh victor@*************
cd /home/<USER>/crawler-notifier

# Install everything (Node.js, Docker, Puppeteer dependencies, etc.)
sudo ./scripts/deploy.sh setup
```

### Step 3: Deploy

```bash
# Docker deployment (recommended)
sudo ./scripts/deploy.sh docker

# OR Native deployment with PM2
sudo ./scripts/deploy.sh native
```

## 📦 What Gets Installed Automatically

✅ **Node.js 18.x**  
✅ **Docker & Docker Compose**  
✅ **All Puppeteer/Chrome dependencies** (35+ packages)  
✅ **Application user & directories**  
✅ **Build tools & system dependencies**

## 🔧 Configuration

After deployment, edit these files:

```bash
# Required: Slack webhook URL
nano .env

# Optional: Crawling targets
nano urls.json

# Optional: Proxy settings
nano proxy.config.js
```

## 📋 Management Commands

### Docker Mode

```bash
# View logs
docker-compose -f deployment/docker/docker-compose.yml logs -f

# Restart
docker-compose -f deployment/docker/docker-compose.yml restart

# Stop
docker-compose -f deployment/docker/docker-compose.yml down
```

### PM2 Mode

```bash
# View logs
pm2 logs chotot-crawler

# Restart
pm2 restart chotot-crawler

# Status
pm2 status
```

## 🔄 Updates

```bash
# Update application
sudo ./scripts/deploy.sh update
```

## 🆘 Troubleshooting

### Check if running

```bash
# Docker
docker ps

# PM2
pm2 list

# Process
ps aux | grep node
```

### View logs

```bash
# Application logs
tail -f logs/crawler.log

# Docker logs
docker-compose -f deployment/docker/docker-compose.yml logs -f

# PM2 logs
pm2 logs chotot-crawler
```

### Test configuration

```bash
npm run test-config
```

## 📱 Slack Setup

1. Go to https://api.slack.com/apps
2. Create new app → "From scratch"
3. Enable "Incoming Webhooks"
4. Add webhook to workspace
5. Copy webhook URL to `.env` file

## 🎯 Quick Health Check

```bash
# Test configuration
npm run test-config

# Check sessions
npm run sessions

# View help
npm start -- help
```

## 📁 Important Paths

```
/home/<USER>/crawler-notifier/           # Application directory
/home/<USER>/crawler-notifier/data/      # Runtime data
/home/<USER>/crawler-notifier/output/    # Crawled results
/home/<USER>/crawler-notifier/logs/      # Application logs
/home/<USER>/crawler-notifier/.env       # Configuration
```

## 🔐 Security Notes

- Application runs as `crawler` user (not root)
- Docker containers use non-root user
- Logs are rotated automatically
- Sensitive data in `.env` file only

---

**Need help?** Check the full [Deployment Guide](docs/DEPLOYMENT.md) or [Migration Guide](MIGRATION_GUIDE.md)
