/**
 * Centralized Logging Service for Chotot House Rent Crawler
 * Provides structured logging with file output, console output, and different log levels
 */

const fs = require("fs-extra");
const path = require("path");
const chalk = require("chalk");

class Logger {
  constructor(options = {}) {
    this.logLevel = options.logLevel || process.env.LOG_LEVEL || "info";
    this.logToFile =
      options.logToFile !== false && process.env.LOG_TO_FILE !== "false";
    this.logDir = options.logDir || "./logs";
    this.logFile = options.logFile || "application.log";
    this.maxFileSize = options.maxFileSize || 10 * 1024 * 1024; // 10MB
    this.maxFiles = options.maxFiles || 5;
    this.initialized = false;

    // Log levels with priorities
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
    };

    // Colors for console output
    this.colors = {
      error: chalk.red,
      warn: chalk.yellow,
      info: chalk.blue,
      debug: chalk.gray,
      success: chalk.green,
    };

    // Initialize asynchronously
    this.init().catch(console.error);
  }

  /**
   * Initialize logger - create log directory and files
   */
  async init() {
    if (this.logToFile) {
      try {
        await fs.ensureDir(this.logDir);
        this.logFilePath = path.join(this.logDir, this.logFile);

        // Create log file if it doesn't exist
        if (!(await fs.pathExists(this.logFilePath))) {
          await fs.writeFile(this.logFilePath, "");
        }
        this.initialized = true;
      } catch (error) {
        console.error("Failed to initialize logger:", error.message);
        this.logToFile = false;
        this.initialized = false;
      }
    } else {
      this.initialized = true;
    }
  }

  /**
   * Check if log level should be logged
   */
  shouldLog(level) {
    return this.levels[level] <= this.levels[this.logLevel];
  }

  /**
   * Format log message
   */
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      message,
      ...meta,
    };

    return {
      console: `${chalk.gray(timestamp)} ${this.colors[level](
        `[${level.toUpperCase()}]`
      )} ${message}`,
      file: JSON.stringify(logEntry),
    };
  }

  /**
   * Write to log file with rotation
   */
  async writeToFile(message) {
    if (!this.logToFile || !this.logFilePath || !this.initialized) return;

    try {
      // Check file size and rotate if necessary
      const stats = await fs.stat(this.logFilePath).catch(() => ({ size: 0 }));
      if (stats.size > this.maxFileSize) {
        await this.rotateLogFile();
      }

      await fs.appendFile(this.logFilePath, message + "\n");
    } catch (error) {
      console.error("Failed to write to log file:", error.message);
    }
  }

  /**
   * Rotate log files
   */
  async rotateLogFile() {
    try {
      const logDir = path.dirname(this.logFilePath);
      const logName = path.basename(this.logFilePath, ".log");

      // Move existing numbered logs
      for (let i = this.maxFiles - 1; i > 0; i--) {
        const oldFile = path.join(logDir, `${logName}.${i}.log`);
        const newFile = path.join(logDir, `${logName}.${i + 1}.log`);

        if (await fs.pathExists(oldFile)) {
          if (i === this.maxFiles - 1) {
            await fs.remove(oldFile); // Remove oldest log
          } else {
            await fs.move(oldFile, newFile);
          }
        }
      }

      // Move current log to .1
      const firstRotated = path.join(logDir, `${logName}.1.log`);
      if (await fs.pathExists(this.logFilePath)) {
        await fs.move(this.logFilePath, firstRotated);
      }

      // Create new log file
      await fs.writeFile(this.logFilePath, "");
    } catch (error) {
      console.error("Failed to rotate log file:", error.message);
    }
  }

  /**
   * Generic log method
   */
  async log(level, message, meta = {}) {
    if (!this.shouldLog(level)) return;

    const formatted = this.formatMessage(level, message, meta);

    // Console output
    console.log(formatted.console);

    // File output
    await this.writeToFile(formatted.file);
  }

  /**
   * Error logging
   */
  async error(message, meta = {}) {
    await this.log("error", message, meta);
  }

  /**
   * Warning logging
   */
  async warn(message, meta = {}) {
    await this.log("warn", message, meta);
  }

  /**
   * Info logging
   */
  async info(message, meta = {}) {
    await this.log("info", message, meta);
  }

  /**
   * Debug logging
   */
  async debug(message, meta = {}) {
    await this.log("debug", message, meta);
  }

  /**
   * Success logging (special case of info)
   */
  async success(message, meta = {}) {
    const formatted = this.formatMessage("info", message, {
      ...meta,
      type: "success",
    });

    // Console output with success color
    console.log(
      `${chalk.gray(new Date().toISOString())} ${this.colors.success(
        "[SUCCESS]"
      )} ${message}`
    );

    // File output
    await this.writeToFile(formatted.file);
  }

  /**
   * Log crawler activity with specific metadata
   */
  async logCrawlerActivity(activity, status, details = {}) {
    const meta = {
      activity,
      status,
      ...details,
      component: "crawler",
    };

    const message = `${activity}: ${status}`;

    if (status === "success" || status === "completed") {
      await this.success(message, meta);
    } else if (status === "error" || status === "failed") {
      await this.error(message, meta);
    } else if (status === "warning") {
      await this.warn(message, meta);
    } else {
      await this.info(message, meta);
    }
  }

  /**
   * Log system activity
   */
  async logSystemActivity(component, action, status, details = {}) {
    const meta = {
      component,
      action,
      status,
      ...details,
    };

    const message = `${component} - ${action}: ${status}`;

    if (status === "success" || status === "completed") {
      await this.success(message, meta);
    } else if (status === "error" || status === "failed") {
      await this.error(message, meta);
    } else if (status === "warning") {
      await this.warn(message, meta);
    } else {
      await this.info(message, meta);
    }
  }

  /**
   * Get log statistics
   */
  async getLogStats() {
    if (!this.logToFile || !this.logFilePath || !this.initialized) {
      return { error: "File logging not enabled or not initialized" };
    }

    try {
      const stats = await fs.stat(this.logFilePath);
      const content = await fs.readFile(this.logFilePath, "utf8");
      const lines = content.split("\n").filter((line) => line.trim());

      const logCounts = {
        error: 0,
        warn: 0,
        info: 0,
        debug: 0,
      };

      lines.forEach((line) => {
        try {
          const logEntry = JSON.parse(line);
          const level = logEntry.level.toLowerCase();
          if (logCounts.hasOwnProperty(level)) {
            logCounts[level]++;
          }
        } catch (e) {
          // Skip invalid JSON lines
        }
      });

      return {
        fileSize: stats.size,
        totalEntries: lines.length,
        lastModified: stats.mtime,
        logCounts,
      };
    } catch (error) {
      return { error: error.message };
    }
  }

  /**
   * Clear log file
   */
  async clearLogs() {
    if (!this.logToFile || !this.logFilePath) return false;

    try {
      await fs.writeFile(this.logFilePath, "");
      await this.info("Log file cleared");
      return true;
    } catch (error) {
      console.error("Failed to clear log file:", error.message);
      return false;
    }
  }
}

// Create singleton instance
const logger = new Logger();

module.exports = logger;
