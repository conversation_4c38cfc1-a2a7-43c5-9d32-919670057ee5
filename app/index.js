#!/usr/bin/env node

/**
 * Main application entry point for the Rental Property Crawler
 * This is the single entry point that handles both CLI and scheduled modes
 */

// Load environment variables first
require("dotenv").config();

const path = require("path");
const chalk = require("chalk");

// Determine the mode based on command line arguments or environment
const args = process.argv.slice(2);
const isScheduledMode =
  process.env.NODE_ENV === "production" || args.includes("--scheduled");
const isCliMode = args.length > 0 && !isScheduledMode;

async function main() {
  try {
    console.log(chalk.blue.bold("\n🏠 Rental Property Crawler"));
    console.log(chalk.gray("Advanced web crawler for rental property data\n"));

    if (isScheduledMode) {
      // Run in scheduled mode with Slack notifications
      console.log(chalk.green("🔄 Starting in scheduled mode..."));
      const ScheduledCrawler = require("./core/scheduled-crawler");
      const crawler = new ScheduledCrawler();
      await crawler.start();
    } else if (isCliMode) {
      // Run CLI commands
      console.log(chalk.green("⚡ Starting CLI mode..."));
      const CLI = require("./cli");
      const cli = new CLI();
      await cli.run(args);
    } else {
      // Default: run single crawl
      console.log(chalk.green("🚀 Starting single crawl..."));
      const RentalCrawler = require("./core/crawler");
      const crawler = new RentalCrawler();
      await crawler.run();
    }

    process.exit(0);
  } catch (error) {
    console.error(chalk.red("\n💥 Fatal error:"), error.message);
    if (process.env.NODE_ENV !== "production") {
      console.error(chalk.gray(error.stack));
    }
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on("SIGINT", () => {
  console.log(
    chalk.yellow("\n⚠️ Received SIGINT, shutting down gracefully...")
  );
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.log(
    chalk.yellow("\n⚠️ Received SIGTERM, shutting down gracefully...")
  );
  process.exit(0);
});

// Handle unhandled promise rejections
process.on("unhandledRejection", (reason, promise) => {
  console.error(
    chalk.red("❌ Unhandled Rejection at:"),
    promise,
    "reason:",
    reason
  );
  process.exit(1);
});

// Handle uncaught exceptions
process.on("uncaughtException", (error) => {
  console.error(chalk.red("❌ Uncaught Exception:"), error);
  process.exit(1);
});

if (require.main === module) {
  main();
}

module.exports = main;
