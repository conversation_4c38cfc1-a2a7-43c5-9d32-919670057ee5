/**
 * Main crawler class for rental property data extraction
 */

const fs = require("fs-extra");
const path = require("path");
const chalk = require("chalk");
const StealthBrowser = require("../utils/stealth");
const ChototExtractor = require("../extractors/chotot");
const DemoExtractor = require("../extractors/demo");
const ProxyManager = require("../utils/proxy");
const CaptchaHandler = require("../utils/captcha");
const SessionManager = require("../utils/session");
const logger = require("../services/logger");
const config = require("../../config");

class RentalCrawler {
  constructor(options = {}) {
    this.config = config;
    this.browser = new StealthBrowser(config);
    this.proxyManager = new ProxyManager(config);
    this.captchaHandler = new CaptchaHandler(config);
    this.sessionManager = new SessionManager(config);
    this.extractors = {
      chotot: new ChototExtractor(config),
      demo: new DemoExtractor(config),
    };
    this.allProperties = [];
    this.options = {
      resumeSession: options.resumeSession || null,
      createNewSession: options.createNewSession || true,
      enableProxy: options.enableProxy !== false,
      enableCaptchaSolving: options.enableCaptchaSolving || false,
      ...options,
    };
  }

  async initialize() {
    try {
      console.log(chalk.blue("🚀 Initializing Rental Property Crawler..."));
      await logger.logCrawlerActivity("initialization", "started");

      // Ensure output directory exists
      await fs.ensureDir(this.config.output.directory);
      await logger.logSystemActivity(
        "filesystem",
        "create_output_directory",
        "success",
        {
          directory: this.config.output.directory,
        }
      );

      // Initialize session manager
      await this.sessionManager.initialize();
      await logger.logSystemActivity(
        "session_manager",
        "initialize",
        "success"
      );

      // Handle session resumption
      if (this.options.resumeSession) {
        const loaded = await this.sessionManager.loadSession(
          this.options.resumeSession
        );
        if (!loaded) {
          console.log(
            chalk.yellow(
              `⚠️ Could not load session ${this.options.resumeSession}, creating new one`
            )
          );
          await logger.logSystemActivity(
            "session_manager",
            "load_session",
            "failed",
            {
              sessionId: this.options.resumeSession,
            }
          );
          await this.sessionManager.createSession();
          await logger.logSystemActivity(
            "session_manager",
            "create_session",
            "success"
          );
        } else {
          await logger.logSystemActivity(
            "session_manager",
            "load_session",
            "success",
            {
              sessionId: this.options.resumeSession,
            }
          );
        }
      } else if (this.options.createNewSession) {
        await this.sessionManager.createSession();
        await logger.logSystemActivity(
          "session_manager",
          "create_session",
          "success"
        );
      }

      // Initialize proxy manager
      if (this.options.enableProxy) {
        await this.proxyManager.initialize();
        await logger.logSystemActivity(
          "proxy_manager",
          "initialize",
          "success"
        );
      }

      // Launch browser with proxy support
      this.page = await this.browser.launch(this.proxyManager);
      await logger.logSystemActivity("browser", "launch", "success");

      // Apply session data to page
      if (this.sessionManager.currentSession) {
        await this.sessionManager.applySessionToPage(this.page);
        this.sessionManager.startAutoSave();
        await logger.logSystemActivity(
          "session_manager",
          "apply_session",
          "success"
        );
      }

      console.log(chalk.green("✅ Crawler initialized successfully"));
      await logger.logCrawlerActivity("initialization", "completed");
    } catch (error) {
      console.error(chalk.red("❌ Failed to initialize crawler:"), error);
      await logger.logCrawlerActivity("initialization", "failed", {
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  async loadUrls() {
    try {
      const urlsPath = path.join(process.cwd(), "urls.json");
      const urlsData = await fs.readJson(urlsPath);

      console.log(
        chalk.blue(`📋 Loaded ${urlsData.site.length} site(s) to crawl`)
      );
      return urlsData.site;
    } catch (error) {
      console.error(chalk.red("❌ Failed to load URLs:"), error);
      throw error;
    }
  }

  async crawlSite(siteConfig) {
    try {
      console.log(chalk.yellow(`\n🏠 Starting to crawl ${siteConfig.name}...`));
      console.log(chalk.gray(`URL: ${siteConfig.url}`));
      await logger.logCrawlerActivity("site_crawl", "started", {
        siteName: siteConfig.name,
        url: siteConfig.url,
      });

      const extractor = this.getExtractor(siteConfig.name);
      if (!extractor) {
        const error = new Error(
          `No extractor found for site: ${siteConfig.name}`
        );
        await logger.logCrawlerActivity("site_crawl", "failed", {
          siteName: siteConfig.name,
          error: error.message,
        });
        throw error;
      }

      // Navigate to the site
      await this.browser.navigateWithRetry(siteConfig.url);
      await logger.logSystemActivity("browser", "navigate", "success", {
        url: siteConfig.url,
      });

      // Wait for page to load
      await this.browser.randomDelay(2000, 4000);

      let pageCount = 0;
      let totalProperties = 0;
      const maxPages = this.config.crawling.maxPages;

      while (pageCount < maxPages) {
        pageCount++;
        console.log(
          chalk.blue(`\n📄 Processing page ${pageCount}/${maxPages}...`)
        );

        try {
          // Scroll to load more content if needed
          await extractor.scrollToLoadMore(
            this.page,
            this.config.sites.chotot.maxScrolls
          );

          // Extract property data
          const properties = await extractor.extractPropertyData(this.page);

          if (properties.length === 0) {
            console.log(chalk.yellow("⚠️ No properties found on this page"));
            await logger.logCrawlerActivity("page_extraction", "warning", {
              siteName: siteConfig.name,
              pageNumber: pageCount,
              propertiesFound: 0,
            });
            break;
          }

          // Clean and add properties
          const cleanedProperties = properties.map((prop) =>
            extractor.cleanPropertyData(prop)
          );
          this.allProperties.push(...cleanedProperties);
          totalProperties += cleanedProperties.length;

          console.log(
            chalk.green(
              `✅ Extracted ${cleanedProperties.length} properties from page ${pageCount}`
            )
          );
          await logger.logCrawlerActivity("page_extraction", "success", {
            siteName: siteConfig.name,
            pageNumber: pageCount,
            propertiesFound: cleanedProperties.length,
            totalProperties: totalProperties,
          });

          // Check if there's a next page
          const hasNext = await extractor.hasNextPage(this.page);
          if (!hasNext) {
            console.log(chalk.blue("📄 No more pages available"));
            break;
          }

          // Navigate to next page
          const navigated = await extractor.goToNextPage(this.page);
          if (!navigated) {
            console.log(chalk.yellow("⚠️ Could not navigate to next page"));
            break;
          }

          // Random delay between pages
          await this.browser.randomDelay(
            this.config.crawling.delay.min,
            this.config.crawling.delay.max
          );
        } catch (error) {
          console.error(
            chalk.red(`❌ Error on page ${pageCount}:`),
            error.message
          );
          await logger.logCrawlerActivity("page_extraction", "error", {
            siteName: siteConfig.name,
            pageNumber: pageCount,
            error: error.message,
            stack: error.stack,
          });

          // Try to continue with next page
          if (pageCount < maxPages) {
            console.log(chalk.yellow("🔄 Attempting to continue..."));
            await logger.logCrawlerActivity("page_extraction", "retry", {
              siteName: siteConfig.name,
              pageNumber: pageCount,
            });
            await this.browser.randomDelay(5000, 8000);
          }
        }
      }

      console.log(chalk.green(`\n✅ Completed crawling ${siteConfig.name}`));
      console.log(
        chalk.green(`📊 Total properties extracted: ${totalProperties}`)
      );
      await logger.logCrawlerActivity("site_crawl", "completed", {
        siteName: siteConfig.name,
        totalProperties: totalProperties,
        pagesProcessed: pageCount,
      });

      return totalProperties;
    } catch (error) {
      console.error(chalk.red(`❌ Failed to crawl ${siteConfig.name}:`), error);
      await logger.logCrawlerActivity("site_crawl", "failed", {
        siteName: siteConfig.name,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  getExtractor(siteName) {
    const normalizedName = siteName.toLowerCase();

    if (normalizedName.includes("demo")) {
      return this.extractors.demo;
    } else if (
      normalizedName.includes("chotot") ||
      normalizedName.includes("nhatot")
    ) {
      return this.extractors.chotot;
    }

    return null;
  }

  async saveResults() {
    try {
      if (this.allProperties.length === 0) {
        console.log(chalk.yellow("⚠️ No properties to save"));
        await logger.logCrawlerActivity("save_results", "warning", {
          reason: "no_properties_found",
        });
        return;
      }

      await logger.logCrawlerActivity("save_results", "started", {
        totalProperties: this.allProperties.length,
      });

      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const filename = `${this.config.output.filename}_${timestamp}`;

      // Save as JSON
      const jsonPath = path.join(
        this.config.output.directory,
        `${filename}.json`
      );
      await fs.writeJson(
        jsonPath,
        {
          metadata: {
            totalProperties: this.allProperties.length,
            extractedAt: new Date().toISOString(),
            sites: [...new Set(this.allProperties.map((p) => p.source))],
          },
          properties: this.allProperties,
        },
        { spaces: 2 }
      );

      await logger.logSystemActivity("filesystem", "save_json", "success", {
        filePath: jsonPath,
        totalProperties: this.allProperties.length,
      });

      // Save as CSV if requested
      if (this.config.output.format === "csv") {
        const csvPath = path.join(
          this.config.output.directory,
          `${filename}.csv`
        );
        await this.saveAsCSV(csvPath);
        await logger.logSystemActivity("filesystem", "save_csv", "success", {
          filePath: csvPath,
        });
      }

      console.log(chalk.green(`\n💾 Results saved:`));
      console.log(chalk.gray(`📁 JSON: ${jsonPath}`));
      console.log(
        chalk.gray(`📊 Total properties: ${this.allProperties.length}`)
      );

      await logger.logCrawlerActivity("save_results", "completed", {
        totalProperties: this.allProperties.length,
        jsonPath: jsonPath,
        formats:
          this.config.output.format === "csv" ? ["json", "csv"] : ["json"],
      });
    } catch (error) {
      console.error(chalk.red("❌ Failed to save results:"), error);
      await logger.logCrawlerActivity("save_results", "failed", {
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  async saveAsCSV(csvPath) {
    // Simple CSV conversion
    if (this.allProperties.length === 0) return;

    const headers = Object.keys(this.allProperties[0]).filter(
      (key) => typeof this.allProperties[0][key] !== "object"
    );
    const csvContent = [
      headers.join(","),
      ...this.allProperties.map((prop) =>
        headers
          .map((header) => {
            const value = prop[header] || "";
            return `"${String(value).replace(/"/g, '""')}"`;
          })
          .join(",")
      ),
    ].join("\n");

    await fs.writeFile(csvPath, csvContent);
    console.log(chalk.gray(`📊 CSV: ${csvPath}`));
  }

  async run() {
    const startTime = Date.now();
    try {
      await logger.logCrawlerActivity("crawl_session", "started");

      await this.initialize();

      const sites = await this.loadUrls();
      await logger.logCrawlerActivity("load_urls", "success", {
        sitesCount: sites.length,
      });

      for (const site of sites) {
        await this.crawlSite(site);
      }

      await this.saveResults();

      const duration = Date.now() - startTime;
      console.log(chalk.green("\n🎉 Crawling completed successfully!"));
      await logger.logCrawlerActivity("crawl_session", "completed", {
        totalProperties: this.allProperties.length,
        sitesProcessed: sites.length,
        durationMs: duration,
        durationMinutes: Math.round((duration / 60000) * 100) / 100,
      });
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(chalk.red("\n💥 Crawling failed:"), error);
      await logger.logCrawlerActivity("crawl_session", "failed", {
        error: error.message,
        stack: error.stack,
        durationMs: duration,
      });
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  async cleanup() {
    try {
      await logger.logSystemActivity("browser", "cleanup", "started");
      await this.browser.close();
      console.log(chalk.blue("🧹 Cleanup completed"));
      await logger.logSystemActivity("browser", "cleanup", "completed");
    } catch (error) {
      console.error(chalk.red("❌ Cleanup error:"), error);
      await logger.logSystemActivity("browser", "cleanup", "failed", {
        error: error.message,
      });
    }
  }
}

module.exports = RentalCrawler;
