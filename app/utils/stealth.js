/**
 * Stealth utilities to bypass <PERSON><PERSON>lare and other bot detection
 */

const puppeteer = require("puppeteer-extra");
const StealthPlugin = require("puppeteer-extra-plugin-stealth");
const AdblockerPlugin = require("puppeteer-extra-plugin-adblocker");

// Add stealth plugin
puppeteer.use(StealthPlugin());

// Add adblocker plugin to reduce detection
puppeteer.use(AdblockerPlugin({ blockTrackers: true }));

class StealthBrowser {
  constructor(config) {
    this.config = config;
    this.browser = null;
    this.page = null;
  }

  async launch(proxyManager = null) {
    try {
      // Get proxy arguments if proxy manager is provided
      const proxyArgs = proxyManager ? proxyManager.getProxyArgs() : [];

      this.browser = await puppeteer.launch({
        headless: this.config.browser.headless,
        args: [
          ...this.config.browser.args,
          ...proxyArgs,
          "--disable-blink-features=AutomationControlled",
          "--disable-features=VizDisplayCompositor",
          "--disable-ipc-flooding-protection",
        ],
        defaultViewport: null,
        ignoreDefaultArgs: ["--enable-automation"],
        executablePath: `/usr/bin/google-chrome`,
      });

      this.page = await this.browser.newPage();

      // Set proxy authentication if needed
      if (proxyManager) {
        const proxyAuth = proxyManager.getProxyAuth();
        if (proxyAuth) {
          await this.page.authenticate(proxyAuth);
          console.log("🔐 Proxy authentication set");
        }
      }

      // Remove webdriver property
      await this.page.evaluateOnNewDocument(() => {
        Object.defineProperty(navigator, "webdriver", {
          get: () => undefined,
        });
      });

      // Set viewport
      await this.page.setViewport(this.config.browser.viewport);

      // Set user agent
      await this.page.setUserAgent(this.config.browser.userAgent);

      // Set additional headers to appear more human-like
      await this.page.setExtraHTTPHeaders({
        "Accept-Language": "vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7",
        "Accept-Encoding": "gzip, deflate, br",
        Accept:
          "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
        Connection: "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Cache-Control": "max-age=0",
      });

      // Override permissions
      const context = this.browser.defaultBrowserContext();
      await context.overridePermissions("https://www.nhatot.com", [
        "geolocation",
      ]);

      // Set geolocation to Vietnam (Ho Chi Minh City)
      await this.page.setGeolocation({
        latitude: 10.8231,
        longitude: 106.6297,
      });

      // Add more realistic browser behavior
      await this.page.evaluateOnNewDocument(() => {
        // Override the `plugins` property to use a custom getter.
        Object.defineProperty(navigator, "plugins", {
          get: () => [1, 2, 3, 4, 5],
        });

        // Override the `languages` property to use a custom getter.
        Object.defineProperty(navigator, "languages", {
          get: () => ["vi-VN", "vi", "en-US", "en"],
        });

        // Override the `permissions` property to use a custom getter.
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) =>
          parameters.name === "notifications"
            ? Promise.resolve({ state: Notification.permission })
            : originalQuery(parameters);
      });

      console.log("✅ Stealth browser launched successfully");
      return this.page;
    } catch (error) {
      console.error("❌ Failed to launch stealth browser:", error);
      throw error;
    }
  }

  async navigateWithRetry(url, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(
          `🌐 Navigating to ${url} (attempt ${attempt}/${maxRetries})`
        );

        await this.page.goto(url, {
          waitUntil: "networkidle2",
          timeout: this.config.crawling.timeout,
        });

        // Wait for potential Cloudflare challenge
        await this.handleCloudflareChallenge();

        console.log("✅ Navigation successful");
        return true;
      } catch (error) {
        console.log(`⚠️ Navigation attempt ${attempt} failed:`, error.message);

        if (attempt === maxRetries) {
          throw new Error(
            `Failed to navigate after ${maxRetries} attempts: ${error.message}`
          );
        }

        // Wait before retry
        await this.randomDelay(3000, 6000);
      }
    }
  }

  async handleCloudflareChallenge() {
    try {
      // Check for Cloudflare challenge indicators
      const challengeSelectors = [
        "#challenge-form",
        ".cf-browser-verification",
        ".cf-checking-browser",
        "[data-ray]",
        ".loading-verifying",
        ".spacer.loading-verifying",
      ];

      // Check page title for "Just a moment"
      const title = await this.page.title();
      const isChallengePage =
        title.includes("Just a moment") ||
        title.includes("Checking your browser");

      let challengeDetected = isChallengePage;

      for (const selector of challengeSelectors) {
        const element = await this.page.$(selector);
        if (element) {
          challengeDetected = true;
          break;
        }
      }

      if (challengeDetected) {
        console.log("🛡️ Cloudflare challenge detected, waiting...");

        // Wait for challenge to complete (up to 60 seconds)
        await this.page.waitForFunction(
          () => {
            const title = document.title;
            return (
              !title.includes("Just a moment") &&
              !title.includes("Checking your browser") &&
              !document.querySelector("#challenge-form") &&
              !document.querySelector(".cf-browser-verification") &&
              !document.querySelector(".cf-checking-browser") &&
              !document.querySelector(".loading-verifying")
            );
          },
          { timeout: 60000 }
        );

        console.log("✅ Cloudflare challenge passed");

        // Additional wait for page to fully load
        await this.randomDelay(3000, 5000);
      }
    } catch (error) {
      console.log("⚠️ Cloudflare challenge handling:", error.message);
      // Continue anyway, might not be a challenge or timeout
      await this.randomDelay(5000, 8000);
    }
  }

  async randomDelay(min = 2000, max = 5000) {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    console.log(`⏳ Waiting ${delay}ms...`);
    await new Promise((resolve) => setTimeout(resolve, delay));
  }

  async humanLikeScroll() {
    // Simulate human-like scrolling
    await this.page.evaluate(() => {
      return new Promise((resolve) => {
        let totalHeight = 0;
        const distance = 100;
        const timer = setInterval(() => {
          const scrollHeight = document.body.scrollHeight;
          window.scrollBy(0, distance);
          totalHeight += distance;

          if (totalHeight >= scrollHeight) {
            clearInterval(timer);
            resolve();
          }
        }, 100);
      });
    });
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
      console.log("🔒 Browser closed");
    }
  }
}

module.exports = StealthBrowser;
