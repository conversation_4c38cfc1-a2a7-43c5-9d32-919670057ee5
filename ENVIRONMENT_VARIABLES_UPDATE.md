# Environment Variables Integration Update

## Overview

The project has been updated to fully utilize environment variables from the `.env` file. Previously, many configuration values were hardcoded in the source files. Now all configuration is centralized and can be controlled through environment variables.

## Files Updated

### 1. Configuration System (`config/index.js`)
- **Enhanced `loadEnvironmentVariables()` method** to support all environment variables from `.env`
- Added support for:
  - Browser settings (`HEADLESS`, `BROWSER_TIMEOUT`)
  - Crawling settings (`MAX_PAGES`, `CRAWLER_MAX_PAGES`, `DELAY_MIN`, `DELAY_MAX`)
  - Proxy settings (`PROXY_ENABLED`, `PROXY_HOST`, `PROXY_PORT`, `PROXY_USERNAME`, `PROXY_PASSWORD`, `PROXY_TYPE`)
  - CAPTCHA settings (`CAPTCHA_ENABLED`, `TWOCAPTCHA_API_KEY`, `ANTICAPTCHA_API_KEY`, `CAPTCHA_MANUAL_ENABLED`, `CAPTCHA_MANUAL_TIMEOUT`)
  - Slack settings (`SLACK_WEBHOOK_URL`, `SLACK_BATCH_SIZE`, `SLACK_BATCH_DELAY`, `NOTIFY_ON_ERROR`, `NOTIFY_ON_SUCCESS`, `NOTIFY_ON_NO_NEW`)
  - Logging settings (`LOG_LEVEL`, `LOG_TO_FILE`)
  - Database settings (`DB_CLEANUP_ENABLED`, `DB_CLEANUP_DAYS`)
  - Crawler settings (`CRAWLER_BASE_URL`, `CRAWLER_SCHEDULE`)

### 2. Default Configuration (`config/default.js`)
- **Added missing configuration sections**:
  - Slack notification preferences
  - Logging configuration
  - Database configuration
  - Schedule configuration
  - Environment configuration

### 3. Scheduled Crawler (`app/core/scheduled-crawler.js`)
- **Updated to use configuration values** instead of hardcoded values:
  - Base URL now uses `CRAWLER_BASE_URL` from environment
  - Max pages uses `CRAWLER_MAX_PAGES` from environment
  - Cron schedule uses `CRAWLER_SCHEDULE` from environment
- **Added config import** and proper configuration usage

### 4. Slack Service (`app/services/slack.js`)
- **Enhanced to use all Slack-related environment variables**:
  - Batch size and delay from `SLACK_BATCH_SIZE` and `SLACK_BATCH_DELAY`
  - Notification preferences from `NOTIFY_ON_ERROR`, `NOTIFY_ON_SUCCESS`, `NOTIFY_ON_NO_NEW`
- **Added notification control logic** to respect user preferences
- **Improved configuration loading** with fallbacks

### 5. Main Entry Point (`app/index.js`)
- **Added `require('dotenv').config()`** at the top to ensure environment variables are loaded before any other modules

### 6. Package.json
- **Added `test-env` script** to verify environment variable loading

## New Features

### Environment Variable Testing
- **New test script**: `npm run test-env`
- **Validates** that all environment variables are loaded correctly
- **Shows current configuration values** for debugging
- **Identifies configuration issues** automatically

### Notification Control
- **Granular control** over Slack notifications:
  - `NOTIFY_ON_ERROR=true/false` - Control error notifications
  - `NOTIFY_ON_SUCCESS=true/false` - Control success notifications  
  - `NOTIFY_ON_NO_NEW=true/false` - Control "no new properties" notifications

### Flexible Configuration
- **Environment-specific overrides** still work
- **External config files** (proxy.config.js, captcha.config.js) still supported
- **Fallback values** ensure the system works even with missing environment variables

## Environment Variables Supported

All variables from your `.env` file are now fully supported:

```bash
# Slack Configuration
SLACK_WEBHOOK_URL=your_webhook_url
SLACK_BATCH_SIZE=30
SLACK_BATCH_DELAY=1000
NOTIFY_ON_ERROR=true
NOTIFY_ON_SUCCESS=true
NOTIFY_ON_NO_NEW=false

# Application Configuration
NODE_ENV=production
TZ=Asia/Ho_Chi_Minh

# Crawler Configuration
CRAWLER_BASE_URL=your_base_url
CRAWLER_MAX_PAGES=5
CRAWLER_SCHEDULE=0 */4 * * *

# Browser Settings
HEADLESS=true
BROWSER_TIMEOUT=30000

# Crawling Settings
MAX_PAGES=10
DELAY_MIN=2000
DELAY_MAX=5000

# Proxy Configuration
PROXY_ENABLED=false
PROXY_HOST=your_proxy_host
PROXY_PORT=8080
PROXY_USERNAME=your_username
PROXY_PASSWORD=your_password
PROXY_TYPE=http

# CAPTCHA Configuration
CAPTCHA_ENABLED=false
TWOCAPTCHA_API_KEY=your_api_key
ANTICAPTCHA_API_KEY=your_api_key
CAPTCHA_MANUAL_ENABLED=true
CAPTCHA_MANUAL_TIMEOUT=300000

# Logging Configuration
LOG_LEVEL=info
LOG_TO_FILE=true

# Database Configuration
DB_CLEANUP_ENABLED=true
DB_CLEANUP_DAYS=30
```

## Testing

Run the environment variable test to verify everything is working:

```bash
npm run test-env
```

This will show you:
- Which environment variables are loaded
- Current configuration values
- Any configuration issues

## Benefits

1. **Centralized Configuration**: All settings in one place (`.env` file)
2. **Environment-Specific**: Easy to have different settings for development/production
3. **Secure**: Sensitive values (API keys, webhooks) not hardcoded in source
4. **Flexible**: Easy to change settings without code modifications
5. **Validated**: Built-in testing to ensure configuration is correct

## Backward Compatibility

- All existing functionality preserved
- External config files still work
- Default values ensure system works without `.env` file
- No breaking changes to existing workflows
