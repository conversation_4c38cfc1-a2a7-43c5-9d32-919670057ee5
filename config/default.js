/**
 * Default configuration settings for the house rental crawler
 */

module.exports = {
  // Browser settings
  browser: {
    headless: true, // Set to false for debugging
    viewport: {
      width: 1366,
      height: 768,
    },
    userAgent:
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    args: [
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-dev-shm-usage",
      "--disable-accelerated-2d-canvas",
      "--no-first-run",
      "--no-zygote",
      "--disable-gpu",
    ],
  },

  // Crawling settings
  crawling: {
    delay: {
      min: 2000, // Minimum delay between requests (ms)
      max: 5000, // Maximum delay between requests (ms)
    },
    retries: 3,
    timeout: 30000, // Page load timeout (ms)
    maxPages: 10, // Maximum pages to crawl per site
  },

  // Output settings
  output: {
    directory: "./output",
    format: "json", // json, csv
    filename: "rental_properties",
  },

  // Site-specific settings
  sites: {
    chotot: {
      baseUrl: "https://www.nhatot.com",
      selectors: {
        propertyCard:
          '.ListAds_ListAds__ANK2d li, .AdItem_adItem__gDDQT, .AdItem_adItem__2O6dw, [data-testid="ad-item"], .ad-item, .item-card',
        title:
          'h3, .AdItem_adTitle__1F5Qr, .AdItem_adTitle__3xQOc, [data-testid="ad-title"], .ad-title, .item-title, h2, a[href*="/ad/"]',
        price:
          '.bfe6oav, .szp40s8, [class*="price"], [class*="Price"], .AdItem_adPrice__3xrBT, .AdItem_adPrice__1F5Qr, [data-testid="ad-price"], .ad-price, .price, .item-price',
        location:
          '.c1u6gyxh, [class*="location"], [class*="Location"], .AdItem_adLocation__2O6dw, .AdItem_adLocation__1F5Qr, [data-testid="ad-location"], .ad-location, .location, .item-location',
        area: '.AdItem_adArea__1F5Qr, [data-testid="ad-area"], .ad-area, .area, .item-area, [class*="area"], [class*="Area"]',
        description:
          '.AdItem_adDescription__1F5Qr, [data-testid="ad-description"], .ad-description, .description, .item-description, [class*="description"], [class*="Description"]',
        images:
          'img[src*="cdn.chotot.com"], img[src*="preset:listing"], img[src*="preset:listin"], img, img[data-testid="ad-image"], .ad-image img, .item-image img',
        nextPage:
          '[data-testid="pagination-next"], .pagination-next, .next-page, .btn-next, [class*="next"], [class*="Next"]',
        loadMoreButton:
          '[data-testid="load-more"], .load-more, .btn-load-more, .show-more, [class*="load"], [class*="Load"], [class*="more"], [class*="More"]',
      },
      waitForSelector:
        '.ListAds_ListAds__ANK2d, .AdItem_adItem__gDDQT, .AdItem_adItem__2O6dw, [data-testid="ad-item"], .ad-item, .item-card',
      maxScrolls: 5,
    },
  },

  // Proxy configuration (will be merged with external config)
  proxy: {
    enabled: false,
    host: null,
    port: null,
    username: null,
    password: null,
    type: "http",
  },

  // CAPTCHA configuration (will be merged with external config)
  captcha: {
    enabled: false,
    twoCaptcha: {
      apiKey: null,
    },
    manual: {
      enabled: true,
      timeout: 300000,
    },
  },

  // Slack configuration
  slack: {
    enabled: false,
    webhookUrl: null,
    channel: "#general",
    username: "Property Crawler",
    iconEmoji: ":house:",
    batchSize: 10,
    batchDelay: 1000,
    notifyOnError: true,
    notifyOnSuccess: true,
    notifyOnNoNew: false,
  },

  // Logging configuration
  logging: {
    level: "info",
    file: false,
  },

  // Database configuration
  database: {
    cleanupEnabled: true,
    cleanupDays: 30,
  },

  // Schedule configuration
  schedule: {
    cron: "0 */4 * * *", // Every 4 hours by default
  },

  // Environment configuration
  environment: {
    name: process.env.NODE_ENV || "development",
    timezone: process.env.TZ || "UTC",
  },
};
