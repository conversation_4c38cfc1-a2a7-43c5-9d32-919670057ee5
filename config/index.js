/**
 * Configuration loader for the Rental Property Crawler
 * Loads configuration based on NODE_ENV and merges with defaults
 */

const fs = require("fs");
const path = require("path");
const defaultConfig = require("./default");

class ConfigLoader {
  constructor() {
    this.config = null;
    this.loadConfig();
  }

  loadConfig() {
    // Start with default configuration
    this.config = { ...defaultConfig };

    // Load environment-specific configuration
    const env = process.env.NODE_ENV || "development";
    const envConfigPath = path.join(__dirname, `${env}.js`);

    if (fs.existsSync(envConfigPath)) {
      const envConfig = require(envConfigPath);
      this.config = this.mergeDeep(this.config, envConfig);
      console.log(`📝 Loaded ${env} configuration`);
    }

    // Load external configurations (proxy, captcha)
    this.loadExternalConfigs();

    // Override with environment variables
    this.loadEnvironmentVariables();
  }

  loadExternalConfigs() {
    // Load proxy configuration
    const proxyConfigPath = path.join(process.cwd(), "proxy.config.js");
    if (fs.existsSync(proxyConfigPath)) {
      try {
        const proxyConfig = require(proxyConfigPath);
        this.config.proxy = { ...this.config.proxy, ...proxyConfig };
        console.log("🔗 Loaded proxy configuration");
      } catch (error) {
        console.warn("⚠️ Failed to load proxy configuration:", error.message);
      }
    }

    // Load CAPTCHA configuration
    const captchaConfigPath = path.join(process.cwd(), "captcha.config.js");
    if (fs.existsSync(captchaConfigPath)) {
      try {
        const captchaConfig = require(captchaConfigPath);
        this.config.captcha = { ...this.config.captcha, ...captchaConfig };
        console.log("🤖 Loaded CAPTCHA configuration");
      } catch (error) {
        console.warn("⚠️ Failed to load CAPTCHA configuration:", error.message);
      }
    }
  }

  loadEnvironmentVariables() {
    // Override configuration with environment variables

    // Browser settings
    if (process.env.HEADLESS !== undefined) {
      this.config.browser.headless = process.env.HEADLESS === "true";
    }

    if (process.env.BROWSER_TIMEOUT) {
      this.config.crawling.timeout = parseInt(process.env.BROWSER_TIMEOUT);
    }

    // Crawling settings
    if (process.env.MAX_PAGES) {
      this.config.crawling.maxPages = parseInt(process.env.MAX_PAGES);
    }

    if (process.env.CRAWLER_MAX_PAGES) {
      this.config.crawling.maxPages = parseInt(process.env.CRAWLER_MAX_PAGES);
    }

    if (process.env.DELAY_MIN) {
      this.config.crawling.delay.min = parseInt(process.env.DELAY_MIN);
    }

    if (process.env.DELAY_MAX) {
      this.config.crawling.delay.max = parseInt(process.env.DELAY_MAX);
    }

    // Proxy settings
    if (process.env.PROXY_ENABLED !== undefined) {
      this.config.proxy.enabled = process.env.PROXY_ENABLED === "true";
    }

    if (process.env.PROXY_HOST) {
      this.config.proxy.host = process.env.PROXY_HOST;
    }

    if (process.env.PROXY_PORT) {
      this.config.proxy.port = parseInt(process.env.PROXY_PORT);
    }

    if (process.env.PROXY_USERNAME) {
      this.config.proxy.username = process.env.PROXY_USERNAME;
    }

    if (process.env.PROXY_PASSWORD) {
      this.config.proxy.password = process.env.PROXY_PASSWORD;
    }

    if (process.env.PROXY_TYPE) {
      this.config.proxy.type = process.env.PROXY_TYPE;
    }

    // CAPTCHA settings
    if (process.env.CAPTCHA_ENABLED !== undefined) {
      this.config.captcha.enabled = process.env.CAPTCHA_ENABLED === "true";
    }

    if (process.env.TWOCAPTCHA_API_KEY) {
      this.config.captcha.twoCaptcha = this.config.captcha.twoCaptcha || {};
      this.config.captcha.twoCaptcha.apiKey = process.env.TWOCAPTCHA_API_KEY;
    }

    if (process.env.ANTICAPTCHA_API_KEY) {
      this.config.captcha.antiCaptcha = this.config.captcha.antiCaptcha || {};
      this.config.captcha.antiCaptcha.apiKey = process.env.ANTICAPTCHA_API_KEY;
    }

    if (process.env.CAPTCHA_MANUAL_ENABLED !== undefined) {
      this.config.captcha.manual = this.config.captcha.manual || {};
      this.config.captcha.manual.enabled =
        process.env.CAPTCHA_MANUAL_ENABLED === "true";
    }

    if (process.env.CAPTCHA_MANUAL_TIMEOUT) {
      this.config.captcha.manual = this.config.captcha.manual || {};
      this.config.captcha.manual.timeout = parseInt(
        process.env.CAPTCHA_MANUAL_TIMEOUT
      );
    }

    // Slack settings
    if (process.env.SLACK_WEBHOOK_URL) {
      this.config.slack = this.config.slack || {};
      this.config.slack.webhookUrl = process.env.SLACK_WEBHOOK_URL;
      this.config.slack.enabled = true; // Enable Slack if webhook URL is provided
    }

    if (process.env.SLACK_BATCH_SIZE) {
      this.config.slack = this.config.slack || {};
      this.config.slack.batchSize = parseInt(process.env.SLACK_BATCH_SIZE);
    }

    if (process.env.SLACK_BATCH_DELAY) {
      this.config.slack = this.config.slack || {};
      this.config.slack.batchDelay = parseInt(process.env.SLACK_BATCH_DELAY);
    }

    if (process.env.NOTIFY_ON_ERROR !== undefined) {
      this.config.slack = this.config.slack || {};
      this.config.slack.notifyOnError = process.env.NOTIFY_ON_ERROR === "true";
    }

    if (process.env.NOTIFY_ON_SUCCESS !== undefined) {
      this.config.slack = this.config.slack || {};
      this.config.slack.notifyOnSuccess =
        process.env.NOTIFY_ON_SUCCESS === "true";
    }

    if (process.env.NOTIFY_ON_NO_NEW !== undefined) {
      this.config.slack = this.config.slack || {};
      this.config.slack.notifyOnNoNew = process.env.NOTIFY_ON_NO_NEW === "true";
    }

    // Logging settings
    if (process.env.LOG_LEVEL) {
      this.config.logging = this.config.logging || {};
      this.config.logging.level = process.env.LOG_LEVEL;
    }

    if (process.env.LOG_TO_FILE !== undefined) {
      this.config.logging = this.config.logging || {};
      this.config.logging.file = process.env.LOG_TO_FILE === "true";
    }

    // Database settings
    if (process.env.DB_CLEANUP_ENABLED !== undefined) {
      this.config.database = this.config.database || {};
      this.config.database.cleanupEnabled =
        process.env.DB_CLEANUP_ENABLED === "true";
    }

    if (process.env.DB_CLEANUP_DAYS) {
      this.config.database = this.config.database || {};
      this.config.database.cleanupDays = parseInt(process.env.DB_CLEANUP_DAYS);
    }

    // Crawler base URL
    if (process.env.CRAWLER_BASE_URL) {
      this.config.sites = this.config.sites || {};
      this.config.sites.chotot = this.config.sites.chotot || {};
      this.config.sites.chotot.url = process.env.CRAWLER_BASE_URL;
    }

    // Crawler schedule
    if (process.env.CRAWLER_SCHEDULE) {
      this.config.schedule = this.config.schedule || {};
      this.config.schedule.cron = process.env.CRAWLER_SCHEDULE;
    }

    console.log("🔧 Environment variables loaded into configuration");
  }

  mergeDeep(target, source) {
    const output = Object.assign({}, target);
    if (this.isObject(target) && this.isObject(source)) {
      Object.keys(source).forEach((key) => {
        if (this.isObject(source[key])) {
          if (!(key in target)) Object.assign(output, { [key]: source[key] });
          else output[key] = this.mergeDeep(target[key], source[key]);
        } else {
          Object.assign(output, { [key]: source[key] });
        }
      });
    }
    return output;
  }

  isObject(item) {
    return item && typeof item === "object" && !Array.isArray(item);
  }

  get() {
    return this.config;
  }

  reload() {
    this.loadConfig();
    return this.config;
  }
}

// Export singleton instance
const configLoader = new ConfigLoader();
module.exports = configLoader.get();
