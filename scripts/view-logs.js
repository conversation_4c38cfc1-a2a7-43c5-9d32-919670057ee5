#!/usr/bin/env node

/**
 * Log Viewer Utility for Chotot House Rent Crawler
 * Provides various ways to view and analyze application logs
 */

const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

class LogViewer {
  constructor() {
    this.logDir = './logs';
    this.logFile = 'application.log';
    this.logFilePath = path.join(this.logDir, this.logFile);
  }

  /**
   * Display help information
   */
  showHelp() {
    console.log(chalk.blue.bold('\n📋 Log Viewer - Available Commands\n'));
    console.log(chalk.yellow('Usage: npm run logs [command] [options]\n'));
    
    console.log(chalk.green('Commands:'));
    console.log('  tail [n]     - Show last n lines (default: 50)');
    console.log('  stats        - Show log statistics');
    console.log('  errors       - Show only error entries');
    console.log('  warnings     - Show only warning entries');
    console.log('  search <term> - Search for specific term');
    console.log('  today        - Show today\'s logs');
    console.log('  clear        - Clear log file');
    console.log('  help         - Show this help\n');
    
    console.log(chalk.green('Examples:'));
    console.log('  npm run logs tail 100');
    console.log('  npm run logs errors');
    console.log('  npm run logs search "crawler"');
    console.log('  npm run logs stats\n');
  }

  /**
   * Check if log file exists
   */
  async checkLogFile() {
    if (!(await fs.pathExists(this.logFilePath))) {
      console.log(chalk.yellow('⚠️ No log file found. Run the application first to generate logs.'));
      return false;
    }
    return true;
  }

  /**
   * Read and parse log entries
   */
  async readLogEntries() {
    try {
      const content = await fs.readFile(this.logFilePath, 'utf8');
      const lines = content.split('\n').filter(line => line.trim());
      
      return lines.map(line => {
        try {
          return JSON.parse(line);
        } catch (e) {
          return { 
            timestamp: new Date().toISOString(), 
            level: 'INFO', 
            message: line,
            raw: true 
          };
        }
      });
    } catch (error) {
      console.error(chalk.red('❌ Failed to read log file:'), error.message);
      return [];
    }
  }

  /**
   * Format log entry for display
   */
  formatLogEntry(entry) {
    const timestamp = new Date(entry.timestamp).toLocaleString();
    const level = entry.level.toUpperCase();
    
    let color = chalk.white;
    switch (level) {
      case 'ERROR': color = chalk.red; break;
      case 'WARN': color = chalk.yellow; break;
      case 'INFO': color = chalk.blue; break;
      case 'DEBUG': color = chalk.gray; break;
    }

    let output = `${chalk.gray(timestamp)} ${color(`[${level}]`)} ${entry.message}`;
    
    // Add metadata if present
    if (entry.component || entry.activity || entry.action) {
      const meta = [];
      if (entry.component) meta.push(`component:${entry.component}`);
      if (entry.activity) meta.push(`activity:${entry.activity}`);
      if (entry.action) meta.push(`action:${entry.action}`);
      if (meta.length > 0) {
        output += chalk.gray(` (${meta.join(', ')})`);
      }
    }

    return output;
  }

  /**
   * Show last n lines
   */
  async showTail(lines = 50) {
    if (!(await this.checkLogFile())) return;

    console.log(chalk.blue.bold(`\n📋 Last ${lines} log entries:\n`));
    
    const entries = await this.readLogEntries();
    const tailEntries = entries.slice(-lines);
    
    tailEntries.forEach(entry => {
      console.log(this.formatLogEntry(entry));
    });
    
    console.log(chalk.gray(`\nShowing ${tailEntries.length} of ${entries.length} total entries`));
  }

  /**
   * Show log statistics
   */
  async showStats() {
    if (!(await this.checkLogFile())) return;

    console.log(chalk.blue.bold('\n📊 Log Statistics:\n'));
    
    const entries = await this.readLogEntries();
    const stats = await fs.stat(this.logFilePath);
    
    const counts = {
      ERROR: 0,
      WARN: 0,
      INFO: 0,
      DEBUG: 0
    };

    const components = {};
    const activities = {};
    
    entries.forEach(entry => {
      const level = entry.level.toUpperCase();
      if (counts.hasOwnProperty(level)) {
        counts[level]++;
      }
      
      if (entry.component) {
        components[entry.component] = (components[entry.component] || 0) + 1;
      }
      
      if (entry.activity) {
        activities[entry.activity] = (activities[entry.activity] || 0) + 1;
      }
    });

    console.log(chalk.green('File Information:'));
    console.log(`  File size: ${(stats.size / 1024).toFixed(2)} KB`);
    console.log(`  Last modified: ${stats.mtime.toLocaleString()}`);
    console.log(`  Total entries: ${entries.length}\n`);
    
    console.log(chalk.green('Log Level Distribution:'));
    Object.entries(counts).forEach(([level, count]) => {
      const percentage = entries.length > 0 ? ((count / entries.length) * 100).toFixed(1) : 0;
      console.log(`  ${level}: ${count} (${percentage}%)`);
    });
    
    if (Object.keys(components).length > 0) {
      console.log(chalk.green('\nTop Components:'));
      Object.entries(components)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .forEach(([component, count]) => {
          console.log(`  ${component}: ${count}`);
        });
    }
    
    if (Object.keys(activities).length > 0) {
      console.log(chalk.green('\nTop Activities:'));
      Object.entries(activities)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .forEach(([activity, count]) => {
          console.log(`  ${activity}: ${count}`);
        });
    }
  }

  /**
   * Show only error entries
   */
  async showErrors() {
    if (!(await this.checkLogFile())) return;

    console.log(chalk.red.bold('\n❌ Error Log Entries:\n'));
    
    const entries = await this.readLogEntries();
    const errorEntries = entries.filter(entry => entry.level.toUpperCase() === 'ERROR');
    
    if (errorEntries.length === 0) {
      console.log(chalk.green('✅ No errors found in logs!'));
      return;
    }
    
    errorEntries.forEach(entry => {
      console.log(this.formatLogEntry(entry));
    });
    
    console.log(chalk.gray(`\nFound ${errorEntries.length} error entries`));
  }

  /**
   * Show only warning entries
   */
  async showWarnings() {
    if (!(await this.checkLogFile())) return;

    console.log(chalk.yellow.bold('\n⚠️ Warning Log Entries:\n'));
    
    const entries = await this.readLogEntries();
    const warningEntries = entries.filter(entry => entry.level.toUpperCase() === 'WARN');
    
    if (warningEntries.length === 0) {
      console.log(chalk.green('✅ No warnings found in logs!'));
      return;
    }
    
    warningEntries.forEach(entry => {
      console.log(this.formatLogEntry(entry));
    });
    
    console.log(chalk.gray(`\nFound ${warningEntries.length} warning entries`));
  }

  /**
   * Search for specific term
   */
  async searchLogs(searchTerm) {
    if (!(await this.checkLogFile())) return;

    console.log(chalk.blue.bold(`\n🔍 Search Results for "${searchTerm}":\n`));
    
    const entries = await this.readLogEntries();
    const matchingEntries = entries.filter(entry => 
      entry.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (entry.component && entry.component.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (entry.activity && entry.activity.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    
    if (matchingEntries.length === 0) {
      console.log(chalk.yellow(`No entries found containing "${searchTerm}"`));
      return;
    }
    
    matchingEntries.forEach(entry => {
      console.log(this.formatLogEntry(entry));
    });
    
    console.log(chalk.gray(`\nFound ${matchingEntries.length} matching entries`));
  }

  /**
   * Show today's logs
   */
  async showToday() {
    if (!(await this.checkLogFile())) return;

    console.log(chalk.blue.bold('\n📅 Today\'s Log Entries:\n'));
    
    const entries = await this.readLogEntries();
    const today = new Date().toDateString();
    const todayEntries = entries.filter(entry => 
      new Date(entry.timestamp).toDateString() === today
    );
    
    if (todayEntries.length === 0) {
      console.log(chalk.yellow('No log entries found for today'));
      return;
    }
    
    todayEntries.forEach(entry => {
      console.log(this.formatLogEntry(entry));
    });
    
    console.log(chalk.gray(`\nFound ${todayEntries.length} entries for today`));
  }

  /**
   * Clear log file
   */
  async clearLogs() {
    if (!(await this.checkLogFile())) return;

    try {
      await fs.writeFile(this.logFilePath, '');
      console.log(chalk.green('✅ Log file cleared successfully'));
    } catch (error) {
      console.error(chalk.red('❌ Failed to clear log file:'), error.message);
    }
  }

  /**
   * Run the log viewer with command line arguments
   */
  async run(args) {
    const command = args[0] || 'help';
    
    switch (command) {
      case 'tail':
        const lines = parseInt(args[1]) || 50;
        await this.showTail(lines);
        break;
      case 'stats':
        await this.showStats();
        break;
      case 'errors':
        await this.showErrors();
        break;
      case 'warnings':
        await this.showWarnings();
        break;
      case 'search':
        if (!args[1]) {
          console.log(chalk.red('❌ Please provide a search term'));
          return;
        }
        await this.searchLogs(args[1]);
        break;
      case 'today':
        await this.showToday();
        break;
      case 'clear':
        await this.clearLogs();
        break;
      case 'help':
      default:
        this.showHelp();
        break;
    }
  }
}

// Run if called directly
if (require.main === module) {
  const viewer = new LogViewer();
  const args = process.argv.slice(2);
  viewer.run(args).catch(console.error);
}

module.exports = LogViewer;
