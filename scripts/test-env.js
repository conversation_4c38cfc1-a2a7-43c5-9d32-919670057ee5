#!/usr/bin/env node

/**
 * Test script to verify environment variables are loaded correctly
 */

// Load environment variables
require('dotenv').config();

const config = require('../config');
const chalk = require('chalk');

function testEnvironmentVariables() {
  console.log(chalk.blue.bold('\n🧪 Testing Environment Variables\n'));

  // Test basic environment variables
  console.log(chalk.yellow('📋 Environment Variables:'));
  console.log(`NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);
  console.log(`TZ: ${process.env.TZ || 'undefined'}`);
  console.log(`SLACK_WEBHOOK_URL: ${process.env.SLACK_WEBHOOK_URL ? '✅ Set' : '❌ Not set'}`);
  console.log(`HEADLESS: ${process.env.HEADLESS || 'undefined'}`);
  console.log(`CRAWLER_BASE_URL: ${process.env.CRAWLER_BASE_URL ? '✅ Set' : '❌ Not set'}`);
  console.log(`CRAWLER_SCHEDULE: ${process.env.CRAWLER_SCHEDULE || 'undefined'}`);

  // Test configuration loading
  console.log(chalk.yellow('\n⚙️ Configuration Values:'));
  console.log(`Browser headless: ${config.browser?.headless}`);
  console.log(`Crawling max pages: ${config.crawling?.maxPages}`);
  console.log(`Crawling delay min: ${config.crawling?.delay?.min}ms`);
  console.log(`Crawling delay max: ${config.crawling?.delay?.max}ms`);
  console.log(`Crawling timeout: ${config.crawling?.timeout}ms`);
  
  console.log(`Proxy enabled: ${config.proxy?.enabled}`);
  console.log(`Proxy host: ${config.proxy?.host || 'not set'}`);
  console.log(`Proxy port: ${config.proxy?.port || 'not set'}`);
  
  console.log(`CAPTCHA enabled: ${config.captcha?.enabled}`);
  console.log(`CAPTCHA manual enabled: ${config.captcha?.manual?.enabled}`);
  console.log(`CAPTCHA manual timeout: ${config.captcha?.manual?.timeout}ms`);
  
  console.log(`Slack enabled: ${config.slack?.enabled}`);
  console.log(`Slack webhook URL: ${config.slack?.webhookUrl ? '✅ Set' : '❌ Not set'}`);
  console.log(`Slack batch size: ${config.slack?.batchSize}`);
  console.log(`Slack batch delay: ${config.slack?.batchDelay}ms`);
  console.log(`Slack notify on error: ${config.slack?.notifyOnError}`);
  console.log(`Slack notify on success: ${config.slack?.notifyOnSuccess}`);
  console.log(`Slack notify on no new: ${config.slack?.notifyOnNoNew}`);
  
  console.log(`Logging level: ${config.logging?.level}`);
  console.log(`Logging to file: ${config.logging?.file}`);
  
  console.log(`Database cleanup enabled: ${config.database?.cleanupEnabled}`);
  console.log(`Database cleanup days: ${config.database?.cleanupDays}`);
  
  console.log(`Schedule cron: ${config.schedule?.cron}`);
  console.log(`Crawler base URL: ${config.sites?.chotot?.url || 'not set'}`);

  // Test validation
  console.log(chalk.yellow('\n✅ Validation:'));
  
  const issues = [];
  
  if (!config.slack?.webhookUrl) {
    issues.push('SLACK_WEBHOOK_URL is not set');
  }
  
  if (!config.sites?.chotot?.url) {
    issues.push('CRAWLER_BASE_URL is not set');
  }
  
  if (config.crawling?.delay?.min >= config.crawling?.delay?.max) {
    issues.push('DELAY_MIN should be less than DELAY_MAX');
  }
  
  if (issues.length === 0) {
    console.log(chalk.green('✅ All environment variables are properly configured!'));
  } else {
    console.log(chalk.red('❌ Issues found:'));
    issues.forEach(issue => console.log(chalk.red(`  - ${issue}`)));
  }
  
  console.log(chalk.blue('\n🎯 Test completed!\n'));
}

if (require.main === module) {
  testEnvironmentVariables();
}

module.exports = testEnvironmentVariables;
