# Logging System Documentation

The Chotot House Rent Crawler includes a comprehensive logging system that tracks all application activities, errors, and system events to help you monitor and debug the crawler's operation.

## Features

- **Multiple Log Levels**: Error, Warning, Info, Debug
- **File Logging**: Automatic log rotation and size management
- **Structured Logging**: JSON format for easy parsing and analysis
- **Activity Tracking**: Specialized logging for crawler and system activities
- **Console Output**: Colored console output for development
- **Log Viewer**: Built-in tools to view and analyze logs

## Configuration

### Environment Variables

```bash
# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Enable/disable file logging
LOG_TO_FILE=true
```

### Configuration in Code

The logger can be configured when importing:

```javascript
const logger = require('./app/services/logger');

// Or with custom options
const Logger = require('./app/services/logger');
const customLogger = new Logger({
  logLevel: 'debug',
  logDir: './custom-logs',
  logFile: 'my-app.log',
  maxFileSize: 5 * 1024 * 1024, // 5MB
  maxFiles: 3
});
```

## Usage

### Basic Logging

```javascript
const logger = require('./app/services/logger');

// Basic log levels
await logger.error('Something went wrong');
await logger.warn('This is a warning');
await logger.info('Information message');
await logger.debug('Debug information');
await logger.success('Operation completed successfully');
```

### Crawler Activity Logging

```javascript
// Log crawler-specific activities
await logger.logCrawlerActivity('site_crawl', 'started', {
  siteName: 'chotot.com',
  url: 'https://chotot.com/tp-ho-chi-minh/mua-ban-nha-dat'
});

await logger.logCrawlerActivity('page_extraction', 'success', {
  siteName: 'chotot.com',
  pageNumber: 1,
  propertiesFound: 25,
  totalProperties: 25
});

await logger.logCrawlerActivity('site_crawl', 'completed', {
  siteName: 'chotot.com',
  totalProperties: 150,
  pagesProcessed: 6
});
```

### System Activity Logging

```javascript
// Log system-level activities
await logger.logSystemActivity('browser', 'launch', 'success');

await logger.logSystemActivity('database', 'save', 'success', {
  recordsCount: 100,
  tableName: 'properties'
});

await logger.logSystemActivity('proxy', 'connect', 'failed', {
  proxyUrl: 'http://proxy.example.com:8080',
  error: 'Connection timeout'
});
```

### Error Logging with Context

```javascript
try {
  // Some operation
} catch (error) {
  await logger.logCrawlerActivity('data_extraction', 'failed', {
    error: error.message,
    stack: error.stack,
    context: 'additional context information'
  });
}
```

## Log Viewing Commands

The project includes several npm scripts for viewing and analyzing logs:

```bash
# View help
npm run logs

# Show last 50 log entries (default)
npm run logs:tail

# Show last 100 log entries
npm run logs tail 100

# Show only error entries
npm run logs:errors

# Show only warning entries
npm run logs:warnings

# Show log statistics
npm run logs:stats

# Show today's logs
npm run logs:today

# Search for specific term
npm run logs search "crawler"

# Clear log file
npm run logs:clear
```

## Log File Structure

Logs are stored in JSON format in the `./logs/application.log` file:

```json
{
  "timestamp": "2024-01-15T10:30:45.123Z",
  "level": "INFO",
  "message": "site_crawl: started",
  "activity": "site_crawl",
  "status": "started",
  "component": "crawler",
  "siteName": "chotot.com",
  "url": "https://chotot.com/tp-ho-chi-minh/mua-ban-nha-dat"
}
```

## Log Rotation

The logging system automatically rotates log files when they exceed the configured size (default: 10MB):

- `application.log` - Current log file
- `application.1.log` - Previous log file
- `application.2.log` - Older log file
- ... up to `maxFiles` (default: 5)

## Integration Examples

### In Crawler Code

```javascript
const logger = require('../services/logger');

class MyCrawler {
  async crawlSite(siteConfig) {
    try {
      await logger.logCrawlerActivity('site_crawl', 'started', {
        siteName: siteConfig.name,
        url: siteConfig.url
      });

      // Crawling logic here...

      await logger.logCrawlerActivity('site_crawl', 'completed', {
        siteName: siteConfig.name,
        totalProperties: propertiesFound
      });
    } catch (error) {
      await logger.logCrawlerActivity('site_crawl', 'failed', {
        siteName: siteConfig.name,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }
}
```

### In Service Classes

```javascript
const logger = require('../services/logger');

class DatabaseService {
  async saveProperties(properties) {
    try {
      await logger.logSystemActivity('database', 'save_properties', 'started', {
        count: properties.length
      });

      // Save logic here...

      await logger.logSystemActivity('database', 'save_properties', 'success', {
        count: properties.length,
        saved: savedCount
      });
    } catch (error) {
      await logger.logSystemActivity('database', 'save_properties', 'failed', {
        error: error.message,
        count: properties.length
      });
      throw error;
    }
  }
}
```

## Testing the Logging System

Run the logging test to see the system in action:

```bash
node test-logging.js
```

This will generate sample log entries and show you how the logging system works.

## Monitoring and Alerts

The logging system integrates with the existing monitoring infrastructure:

- **PM2 Logs**: Application logs are also captured by PM2
- **Slack Notifications**: Errors can trigger Slack alerts
- **Log Analysis**: Use the built-in log viewer or external tools

## Best Practices

1. **Use Appropriate Log Levels**:
   - `error`: For actual errors that need attention
   - `warn`: For warnings that might need attention
   - `info`: For general information about application flow
   - `debug`: For detailed debugging information

2. **Include Context**: Always include relevant metadata with your log entries

3. **Don't Log Sensitive Data**: Avoid logging passwords, tokens, or personal information

4. **Use Structured Logging**: Use the specialized logging methods for better organization

5. **Monitor Log File Size**: The system handles rotation automatically, but monitor disk usage

## Troubleshooting

### Log File Not Created

- Check that the `logs` directory exists
- Verify `LOG_TO_FILE` environment variable is not set to `false`
- Check file permissions

### Logs Not Showing in Console

- Verify `LOG_LEVEL` environment variable
- Check if the log level of your message is appropriate

### Performance Impact

- Logging is asynchronous and shouldn't significantly impact performance
- If needed, you can disable file logging by setting `LOG_TO_FILE=false`
